import"../chunks/CWj6FrbW.js";import"../chunks/BrFt5aUl.js";import{o as E}from"../chunks/C1oTikCN.js";import{a2 as O,f as S,t as x,a as d,a3 as B,$ as F,c as s,Z as i,Y as h,s as y,z as p,r as a,n as w,a4 as I,a5 as L}from"../chunks/-lpZNR2G.js";import{h as M,e as T,s as H}from"../chunks/oU013O0A.js";import{i as _}from"../chunks/TVB6uMNP.js";import{i as J}from"../chunks/_QLPfm2f.js";import{s as N,a as V}from"../chunks/DeiEKfcT.js";import{s as Y}from"../chunks/9V84_5nK.js";const Z=()=>{const r=Y;return{page:{subscribe:r.page.subscribe},navigating:{subscribe:r.navigating.subscribe},updated:r.updated}},q={subscribe(r){return Z().page.subscribe(r)}};var G=S('<div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative" role="alert"><span class="block sm:inline"> </span></div>'),K=I('<svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg> Signing in...',1),Q=S('<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8"><div class="max-w-md w-full space-y-8"><div><h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">Sign in to your account</h2> <p class="mt-2 text-center text-sm text-gray-600">Use your Azure Active Directory credentials</p></div> <div class="mt-8 space-y-6"><!> <div><button type="button" class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"><!></button></div> <div class="text-center"><p class="text-xs text-gray-500">By signing in, you agree to our terms of service and privacy policy.</p></div></div></div></div>');function it(r,g){O(g,!1);const[$,A]=N(),k=()=>V(q,"$page",$);let n=h(!1),o=h("");E(()=>{k().url.searchParams.get("error")&&i(o,"Authentication failed. Please try again.")});async function z(){i(n,!0),i(o,"");try{const t=crypto.randomUUID();sessionStorage.setItem("oauth_state",t);const e=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({state:t})});if(e.ok){const{authUrl:l}=await e.json();window.location.href=l}else throw new Error("Failed to initiate login")}catch{i(o,"Failed to initiate login. Please try again."),i(n,!1)}}J();var u=Q();M(t=>{F.title="Login - Azure AD SSO"});var v=s(u),m=y(s(v),2),f=s(m);{var j=t=>{var e=G(),l=s(e),U=s(l,!0);a(l),a(e),x(()=>H(U,p(o))),d(t,e)};_(f,t=>{p(o)&&t(j)})}var b=y(f,2),c=s(b),C=s(c);{var D=t=>{var e=K();w(),d(t,e)},P=t=>{var e=L("Sign in with Azure AD");d(t,e)};_(C,t=>{p(n)?t(D):t(P,!1)})}a(c),a(b),w(2),a(m),a(v),a(u),x(()=>c.disabled=p(n)),T("click",c,z),d(r,u),B(),A()}export{it as component};
