import{F as o,y as v,G as f,I as y,w as n,J as b,K as _,L as d,u as c,M as x,N as h,O as C,P as S,Q as k,R as w}from"./DfYgktsV.js";import{a as A,m as M,u as O}from"./DZXC8P3u.js";import{c as P}from"./imUBLVoF.js";function j(){var t;return f===null&&y(),((t=f).ac??(t.ac=new AbortController)).signal}function p(t){n===null&&o(),d&&n.l!==null?r(n).m.push(t):v(()=>{const e=c(t);if(typeof e=="function")return e})}function D(t){n===null&&o(),p(()=>()=>c(t))}function E(t,e,{bubbles:s=!1,cancelable:l=!1}={}){return new CustomEvent(t,{detail:e,bubbles:s,cancelable:l})}function R(){const t=n;return t===null&&o(),(e,s,l)=>{var u;const a=(u=t.s.$$events)==null?void 0:u[e];if(a){const m=b(a)?a.slice():[a],i=E(e,s,l);for(const g of m)g.call(t.x,i);return!i.defaultPrevented}return!0}}function U(t){n===null&&o(),n.l===null&&_(),r(n).b.push(t)}function $(t){n===null&&o(),n.l===null&&_(),r(n).a.push(t)}function r(t){var e=t.l;return e.u??(e.u={a:[],b:[],m:[]})}const I=Object.freeze(Object.defineProperty({__proto__:null,afterUpdate:$,beforeUpdate:U,createEventDispatcher:R,createRawSnippet:P,flushSync:x,getAbortSignal:j,getAllContexts:h,getContext:C,hasContext:S,hydrate:A,mount:M,onDestroy:D,onMount:p,setContext:k,tick:w,unmount:O,untrack:c},Symbol.toStringTag,{value:"Module"}));export{p as o,I as s};
