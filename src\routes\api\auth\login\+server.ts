import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { AuthService } from '$lib/auth';

export const POST: RequestHandler = async ({ request, platform }) => {
	try {
		const { state } = await request.json();
		
		if (!state) {
			return json({ error: 'State parameter is required' }, { status: 400 });
		}

		// Get environment variables with fallback
		const env = platform?.env && Object.keys(platform.env).length > 0
			? platform.env
			: {
				AZURE_CLIENT_ID: process.env.AZURE_CLIENT_ID || '',
				AZURE_CLIENT_SECRET: process.env.AZURE_CLIENT_SECRET || '',
				AZURE_TENANT_ID: process.env.AZURE_TENANT_ID || '',
				REDIRECT_URI: process.env.REDIRECT_URI || '',
				SESSION_SECRET: process.env.SESSION_SECRET || '',
				COOKIE_DOMAIN: process.env.COOKIE_DOMAIN || 'localhost'
			};

		const authService = new AuthService(env);
		const authUrl = authService.getAuthUrl(state);

		return json({ authUrl });
	} catch (error) {
		console.error('Login API error:', error);
		return json({ error: 'Internal server error' }, { status: 500 });
	}
};
