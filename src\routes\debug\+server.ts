import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { AuthService } from '$lib/auth';

export const GET: RequestHandler = async ({ request, platform, cookies }) => {
	try {
		const cookieHeader = request.headers.get('cookie');
		const sessionCookie = cookies.get('session');

		let session = null;
		if (sessionCookie) {
			try {
				const sessionJson = atob(sessionCookie);
				session = JSON.parse(sessionJson);
			} catch (e) {
				console.log('Error parsing session cookie:', e);
			}
		}

		return json({
			hasCookieHeader: !!cookieHeader,
			cookieHeader: cookieHeader,
			hasSessionCookie: !!sessionCookie,
			sessionCookie: sessionCookie ? 'present' : 'missing',
			hasSession: !!session,
			sessionUser: session?.user?.name || null,
			sessionExpired: session ? Date.now() > session.expiresAt : null,
			environment: platform?.env || {},
			timestamp: new Date().toISOString()
		});
	} catch (error) {
		return json({
			error: error instanceof Error ? error.message : 'Unknown error',
			timestamp: new Date().toISOString()
		}, { status: 500 });
	}
};
