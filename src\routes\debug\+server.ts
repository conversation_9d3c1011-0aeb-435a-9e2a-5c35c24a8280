import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { AuthService } from '$lib/auth';

export const GET: RequestHandler = async ({ request, platform }) => {
	try {
		const cookieHeader = request.headers.get('cookie');
		const authService = new AuthService(platform?.env || {});
		const session = authService.parseSession(cookieHeader);
		
		return json({
			hasCookieHeader: !!cookieHeader,
			cookieHeader: cookieHeader,
			hasSession: !!session,
			sessionUser: session?.user?.name || null,
			environment: platform?.env || {},
			timestamp: new Date().toISOString()
		});
	} catch (error) {
		return json({
			error: error instanceof Error ? error.message : 'Unknown error',
			timestamp: new Date().toISOString()
		}, { status: 500 });
	}
};
