import"../chunks/CWj6FrbW.js";import"../chunks/BrFt5aUl.js";import{f as L,t as V,a as W,$ as B,c as e,s as a,r as t,u as o}from"../chunks/-lpZNR2G.js";import{h as H,e as M,s as r}from"../chunks/oU013O0A.js";import{p as O}from"../chunks/pe1MSJR9.js";var T=L('<div class="min-h-screen bg-gray-50"><nav class="bg-white shadow"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="flex justify-between h-16"><div class="flex items-center"><h1 class="text-xl font-semibold text-gray-900">Home</h1></div> <div class="flex items-center space-x-4"><a href="/dashboard" class="text-blue-600 hover:text-blue-800 px-3 py-2 rounded-md text-sm font-medium">Dashboard</a> <span class="text-sm text-gray-700"> </span> <button type="button" class="bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded-md text-sm font-medium">Logout</button></div></div></div></nav> <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8"><div class="px-4 py-6 sm:px-0"><div class="border-4 border-dashed border-gray-200 rounded-lg p-8"><div class="text-center"><h2 class="text-3xl font-bold text-gray-900 mb-4">Welcome to Azure AD SSO App</h2> <p class="text-gray-600 mb-8">You have successfully authenticated with Azure Active Directory and can access the home page.</p> <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 max-w-4xl mx-auto"><a href="/dashboard" class="bg-blue-600 hover:bg-blue-700 text-white p-6 rounded-lg shadow transition-colors"><div class="text-center"><svg class="h-8 w-8 mx-auto mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path></svg> <h3 class="font-semibold">Dashboard</h3> <p class="text-sm opacity-90">View detailed user information</p></div></a> <div class="bg-green-600 text-white p-6 rounded-lg shadow"><div class="text-center"><svg class="h-8 w-8 mx-auto mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg> <h3 class="font-semibold">Authenticated</h3> <p class="text-sm opacity-90">JWT token validated</p></div></div> <div class="bg-purple-600 text-white p-6 rounded-lg shadow"><div class="text-center"><svg class="h-8 w-8 mx-auto mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path></svg> <h3 class="font-semibold">Cloudflare Workers</h3> <p class="text-sm opacity-90">Running on the edge</p></div></div></div> <div class="mt-8 bg-white rounded-lg shadow p-6 max-w-md mx-auto"><h3 class="text-lg font-medium text-gray-900 mb-4">Current User</h3> <div class="text-left space-y-2"><p class="text-sm"><span class="font-medium text-gray-500">Name:</span> </p> <p class="text-sm"><span class="font-medium text-gray-500">Email:</span> </p> <p class="text-sm"><span class="font-medium text-gray-500">Username:</span> </p></div></div></div></div></div></main></div>');function R(_,k){let A=O(k,"data",8);const{user:s}=A();async function $(){try{(await fetch("/logout",{method:"POST"})).ok&&(window.location.href="/login")}catch(m){console.error("Logout error:",m)}}var d=T();H(m=>{B.title="Home - Azure AD SSO"});var i=e(d),v=e(i),p=e(v),h=a(e(p),2),l=a(e(h),2),z=e(l);t(l);var C=a(l,2);t(h),t(p),t(v),t(i);var x=a(i,2),u=e(x),g=e(u),b=e(g),f=a(e(b),6),w=a(e(f),2),n=e(w),D=a(e(n));t(n);var c=a(n,2),S=a(e(c));t(c);var y=a(c,2),j=a(e(y));t(y),t(w),t(f),t(b),t(g),t(u),t(x),t(d),V(()=>{r(z,`Welcome, ${o(()=>s.name)??""}`),r(D,` ${o(()=>s.name)??""}`),r(S,` ${o(()=>s.email)??""}`),r(j,` ${o(()=>s.preferred_username)??""}`)}),M("click",C,$),W(_,d)}export{R as component};
