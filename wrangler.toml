name = "sveltekit-azuread"
compatibility_date = "2024-01-01"
compatibility_flags = ["nodejs_compat"]

# Default configuration (used for local development)
[vars]
ENVIRONMENT = "develop"
AZURE_CLIENT_ID = "your-dev-client-id"
AZURE_TENANT_ID = "your-dev-tenant-id"
REDIRECT_URI = "http://localhost:8787/callback"
SESSION_SECRET = "your-dev-session-secret-key-32-chars-min"
COOKIE_DOMAIN = "localhost"
AZURE_CLIENT_SECRET = "your-dev-client-secret"

# QA environment
[env.qa.vars]
ENVIRONMENT = "qa"
AZURE_CLIENT_ID = "your-qa-client-id"
AZURE_TENANT_ID = "your-qa-tenant-id"
REDIRECT_URI = "https://your-qa-domain.workers.dev/callback"
SESSION_SECRET = "your-qa-session-secret-key-32-chars-min"
COOKIE_DOMAIN = "your-qa-domain.workers.dev"
AZURE_CLIENT_SECRET = "your-qa-client-secret"

# Production environment
[env.production.vars]
ENVIRONMENT = "production"
AZURE_CLIENT_ID = "your-prod-client-id"
AZURE_TENANT_ID = "your-prod-tenant-id"
REDIRECT_URI = "https://your-domain.com/callback"
SESSION_SECRET = "your-prod-session-secret-key-32-chars-min"
COOKIE_DOMAIN = "your-domain.com"
AZURE_CLIENT_SECRET = "your-prod-client-secret"
