import{a6 as S,a7 as N,D as I,a8 as A,S as B,a9 as F,X as $,G,aa as V,b as X,ab as J,h as w,ac as O,H as W,ad as R,k as v,j as k,o as p,W as j,ae as M,af as L,d as K,ag as Q,ah as U,ai as Z,aj as x,ak as ee,al as te,m as re,a2 as ae,q as ne,R as se,a3 as ie,T as m,u as ue,am as oe}from"./-lpZNR2G.js";const ce=["touchstart","touchmove"];function fe(e){return ce.includes(e)}function le(e){var t=I,a=A;S(null),N(null);try{return e()}finally{S(t),N(a)}}const de=new Set,C=new Set;function _e(e,t,a,n={}){function s(r){if(n.capture||E.call(t,r),!r.cancelBubble)return le(()=>a==null?void 0:a.call(this,r))}return e.startsWith("pointer")||e.startsWith("touch")||e==="wheel"?F(()=>{t.addEventListener(e,s,n)}):t.addEventListener(e,s,n),s}function ge(e,t,a,n,s){var r={capture:n,passive:s},f=_e(e,t,a,r);(t===document.body||t===window||t===document||t instanceof HTMLMediaElement)&&B(()=>{t.removeEventListener(e,f,r)})}function E(e){var H;var t=this,a=t.ownerDocument,n=e.type,s=((H=e.composedPath)==null?void 0:H.call(e))||[],r=s[0]||e.target,f=0,o=e.__root;if(o){var d=s.indexOf(o);if(d!==-1&&(t===document||t===window)){e.__root=t;return}var i=s.indexOf(t);if(i===-1)return;d<=i&&(f=d)}if(r=s[f]||e.target,r!==t){$(e,"currentTarget",{configurable:!0,get(){return r||a}});var D=I,_=A;S(null),N(null);try{for(var u,c=[];r!==null;){var h=r.assignedSlot||r.parentNode||r.host||null;try{var b=r["__"+n];if(b!=null&&(!r.disabled||e.target===r))if(G(b)){var[Y,...z]=b;Y.apply(r,[e,...z])}else b.call(r,e)}catch(T){u?c.push(T):u=T}if(e.cancelBubble||h===t||h===null)break;r=h}if(u){for(let T of c)queueMicrotask(()=>{throw T});throw u}}finally{e.__root=t,delete e.currentTarget,S(D),N(_)}}}let l;function he(){l=void 0}function ve(e){let t=null,a=w;var n;if(w){for(t=p,l===void 0&&(l=j(document.head));l!==null&&(l.nodeType!==O||l.data!==W);)l=R(l);l===null?v(!1):l=k(R(l))}w||(n=document.head.appendChild(V()));try{X(()=>e(n),J)}finally{a&&(v(!0),l=p,k(t))}}function we(e,t){var a=t==null?"":typeof t=="object"?t+"":t;a!==(e.__t??(e.__t=e.nodeValue))&&(e.__t=a,e.nodeValue=a+"")}function be(e,t){return P(e,t)}function Ee(e,t){M(),t.intro=t.intro??!1;const a=t.target,n=w,s=p;try{for(var r=j(a);r&&(r.nodeType!==O||r.data!==W);)r=R(r);if(!r)throw L;v(!0),k(r),K();const f=P(e,{...t,anchor:r});if(p===null||p.nodeType!==O||p.data!==Q)throw U(),L;return v(!1),f}catch(f){if(f===L)return t.recover===!1&&Z(),M(),x(a),v(!1),be(e,t);throw f}finally{v(n),k(s),he()}}const y=new Map;function P(e,{target:t,anchor:a,props:n={},events:s,context:r,intro:f=!0}){M();var o=new Set,d=_=>{for(var u=0;u<_.length;u++){var c=_[u];if(!o.has(c)){o.add(c);var h=fe(c);t.addEventListener(c,E,{passive:h});var b=y.get(c);b===void 0?(document.addEventListener(c,E,{passive:h}),y.set(c,1)):y.set(c,b+1)}}};d(ee(de)),C.add(d);var i=void 0,D=te(()=>{var _=a??t.appendChild(V());return re(()=>{if(r){ae({});var u=ne;u.c=r}s&&(n.$$events=s),w&&se(_,null),i=e(_,n)||{},w&&(A.nodes_end=p),r&&ie()}),()=>{var h;for(var u of o){t.removeEventListener(u,E);var c=y.get(u);--c===0?(document.removeEventListener(u,E),y.delete(u)):y.set(u,c)}C.delete(d),_!==a&&((h=_.parentNode)==null||h.removeChild(_))}});return q.set(i,D),i}let q=new WeakMap;function Te(e,t){const a=q.get(e);return a?(q.delete(e),a(t)):Promise.resolve()}function pe(e,t,a){if(e==null)return t(void 0),m;const n=ue(()=>e.subscribe(t,a));return n.unsubscribe?()=>n.unsubscribe():n}const g=[];function me(e,t=m){let a=null;const n=new Set;function s(o){if(oe(e,o)&&(e=o,a)){const d=!g.length;for(const i of n)i[1](),g.push(i,e);if(d){for(let i=0;i<g.length;i+=2)g[i][0](g[i+1]);g.length=0}}}function r(o){s(o(e))}function f(o,d=m){const i=[o,d];return n.add(i),n.size===1&&(a=t(s,r)||m),o(e),()=>{n.delete(i),n.size===0&&a&&(a(),a=null)}}return{set:s,update:r,subscribe:f}}function Se(e){let t;return pe(e,a=>t=a)(),t}export{Ee as a,pe as b,ge as e,Se as g,ve as h,be as m,we as s,Te as u,me as w};
