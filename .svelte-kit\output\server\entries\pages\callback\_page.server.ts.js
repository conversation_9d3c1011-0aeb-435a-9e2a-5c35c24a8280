import { redirect } from "@sveltejs/kit";
import { A as AuthService } from "../../../chunks/auth.js";
const load = async ({ url, platform, setHeaders }) => {
  const code = url.searchParams.get("code");
  const state = url.searchParams.get("state");
  const error = url.searchParams.get("error");
  if (error) {
    console.error("OAuth error:", error);
    throw redirect(302, "/login?error=oauth_error");
  }
  if (!code || !state) {
    console.error("Missing code or state parameter");
    throw redirect(302, "/login?error=missing_params");
  }
  try {
    const env = platform?.env && Object.keys(platform.env).length > 0 ? platform.env : {
      AZURE_CLIENT_ID: process.env.AZURE_CLIENT_ID || "",
      AZURE_CLIENT_SECRET: process.env.AZURE_CLIENT_SECRET || "",
      AZURE_TENANT_ID: process.env.AZURE_TENANT_ID || "",
      REDIRECT_URI: process.env.REDIRECT_URI || "",
      SESSION_SECRET: process.env.SESSION_SECRET || "",
      COOKIE_DOMAIN: process.env.COOKIE_DOMAIN || "localhost"
    };
    const authService = new AuthService(env);
    const tokenResponse = await authService.exchangeCodeForToken(code);
    if (!tokenResponse.access_token || !tokenResponse.id_token) {
      throw new Error("Invalid token response");
    }
    const user = await authService.validateToken(tokenResponse.id_token);
    const sessionData = {
      user,
      accessToken: tokenResponse.access_token,
      expiresAt: Date.now() + 60 * 60 * 1e3
      // 1 hour from now
    };
    const sessionCookie = authService.createSessionCookie(sessionData);
    setHeaders({
      "Set-Cookie": sessionCookie
    });
    throw redirect(302, "/");
  } catch (error2) {
    console.error("Callback processing error:", error2);
    throw redirect(302, "/login?error=callback_error");
  }
};
export {
  load
};
