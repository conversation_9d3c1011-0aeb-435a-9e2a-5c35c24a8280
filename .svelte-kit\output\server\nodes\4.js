import * as server from '../entries/pages/dashboard/_page.server.ts.js';

export const index = 4;
let component_cache;
export const component = async () => component_cache ??= (await import('../entries/pages/dashboard/_page.svelte.js')).default;
export { server };
export const server_id = "src/routes/dashboard/+page.server.ts";
export const imports = ["_app/immutable/nodes/4.Xnotgmac.js","_app/immutable/chunks/CWj6FrbW.js","_app/immutable/chunks/B4ZCArY7.js","_app/immutable/chunks/DfYgktsV.js","_app/immutable/chunks/DZXC8P3u.js","_app/immutable/chunks/5GeUa9TP.js","_app/immutable/chunks/BrXvW6T5.js"];
export const stylesheets = [];
export const fonts = [];
