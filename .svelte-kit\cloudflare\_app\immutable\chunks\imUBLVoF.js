import{d as _,h as i,e as d,S as h,T as l,V as u,E as v,o as m,W as y,X as g,v as c,Y as E}from"./DfYgktsV.js";function b(t,s,...n){var a=t,e=y,r;_(()=>{e!==(e=s())&&(r&&(g(r),r=null),r=m(()=>e(a,...n)))},v),i&&(a=c)}function R(t){return(s,...n)=>{var f;var a=t(...n),e;if(i)e=c,d();else{var r=a.render().trim(),p=h(r);e=E(p),s.before(e)}const o=(f=a.setup)==null?void 0:f.call(a,e);l(e,e),typeof o=="function"&&u(o)}}export{R as c,b as s};
