import"../chunks/CWj6FrbW.js";import"../chunks/B4ZCArY7.js";import{p as h,f as g,a3 as l,t as v,a as d,b as _,c as e,r as o,s as x}from"../chunks/DfYgktsV.js";import{s as p}from"../chunks/DZXC8P3u.js";import{i as $}from"../chunks/BIhxBn-I.js";import{s as b,p as m}from"../chunks/BRLLTahE.js";const k={get error(){return m.error},get status(){return m.status}};b.updated.check;const i=k;var E=g("<h1> </h1> <p> </p>",1);function B(c,f){h(f,!1),$();var t=E(),r=l(t),n=e(r,!0);o(r);var s=x(r,2),u=e(s,!0);o(s),v(()=>{var a;p(n,i.status),p(u,(a=i.error)==null?void 0:a.message)}),d(c,t),_()}export{B as component};
