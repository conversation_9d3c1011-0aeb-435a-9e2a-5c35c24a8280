import{d as g,h as u,e as h,E as p,g as S,H as k,i as D,j as H,k as q,l as I,m as v,o as _,q as b,U as F,v as L}from"./DfYgktsV.js";function U(m,A,[t,s]=[0,0]){u&&t===0&&h();var a=m,f=null,e=null,i=F,E=t>0?p:0,n=!1;const N=(c,l=!0)=>{n=!0,o(l,c)},o=(c,l)=>{if(i===(i=c))return;let T=!1;if(u&&s!==-1){if(t===0){const r=S(a);r===k?s=0:r===D?s=1/0:(s=parseInt(r.substring(1)),s!==s&&(s=i?1/0:-1))}const R=s>t;!!i===R&&(a=H(),q(a),I(!1),T=!0,s=-1)}i?(f?v(f):l&&(f=_(()=>l(a))),e&&b(e,()=>{e=null})):(e?v(e):l&&(e=_(()=>l(a,[t+1,s]))),f&&b(f,()=>{f=null})),T&&I(!0)};g(()=>{n=!1,A(N),n||o(null,null)},E),u&&(a=L)}export{U as i};
