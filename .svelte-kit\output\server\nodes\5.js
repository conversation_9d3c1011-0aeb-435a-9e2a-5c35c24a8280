

export const index = 5;
let component_cache;
export const component = async () => component_cache ??= (await import('../entries/pages/login/_page.svelte.js')).default;
export const imports = ["_app/immutable/nodes/5.DylUgmBi.js","_app/immutable/chunks/CWj6FrbW.js","_app/immutable/chunks/BrFt5aUl.js","_app/immutable/chunks/-lpZNR2G.js","_app/immutable/chunks/C1oTikCN.js","_app/immutable/chunks/oU013O0A.js","_app/immutable/chunks/DHFUQ1My.js","_app/immutable/chunks/TVB6uMNP.js","_app/immutable/chunks/_QLPfm2f.js","_app/immutable/chunks/DeiEKfcT.js","_app/immutable/chunks/DXfUJeH0.js"];
export const stylesheets = ["_app/immutable/assets/5.tn0RQdqM.css"];
export const fonts = [];
