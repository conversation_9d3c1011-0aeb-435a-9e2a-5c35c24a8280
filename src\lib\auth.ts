import { jwtVerify, createRemoteJWKSet, type JWTPayload } from 'jose';
import { serialize, parse } from 'cookie';
import { redirect } from '@sveltejs/kit';
import type { RequestEvent } from '@sveltejs/kit';

export interface AzureUser {
	oid: string;
	name: string;
	email: string;
	preferred_username: string;
	tid: string;
}

export interface SessionData {
	user: AzureUser;
	accessToken: string;
	expiresAt: number;
}

// Cache for JWKS
let jwksCache: ReturnType<typeof createRemoteJWKSet> | null = null;

export class AuthService {
	private clientId: string;
	private clientSecret: string;
	private tenantId: string;
	private redirectUri: string;
	private sessionSecret: string;
	private cookieDomain: string;

	constructor(env: Record<string, string>) {
		this.clientId = env.AZURE_CLIENT_ID;
		this.clientSecret = env.AZURE_CLIENT_SECRET;
		this.tenantId = env.AZURE_TENANT_ID;
		this.redirectUri = env.REDIRECT_URI;
		this.sessionSecret = env.SESSION_SECRET;
		this.cookieDomain = env.COOKIE_DOMAIN;

		if (!this.clientId || !this.clientSecret || !this.tenantId || !this.redirectUri || !this.sessionSecret) {
			throw new Error('Missing required Azure AD configuration');
		}
	}

	/**
	 * Get Azure AD OAuth2 authorization URL
	 */
	getAuthUrl(state?: string): string {
		const params = new URLSearchParams({
			client_id: this.clientId,
			response_type: 'code',
			redirect_uri: this.redirectUri,
			scope: 'openid profile email',
			response_mode: 'query',
			...(state && { state })
		});

		return `https://login.microsoftonline.com/${this.tenantId}/oauth2/v2.0/authorize?${params}`;
	}

	/**
	 * Exchange authorization code for access token
	 */
	async exchangeCodeForToken(code: string): Promise<{ access_token: string; id_token: string }> {
		const tokenUrl = `https://login.microsoftonline.com/${this.tenantId}/oauth2/v2.0/token`;
		
		const body = new URLSearchParams({
			client_id: this.clientId,
			client_secret: this.clientSecret,
			code,
			grant_type: 'authorization_code',
			redirect_uri: this.redirectUri,
			scope: 'openid profile email'
		});

		const response = await fetch(tokenUrl, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/x-www-form-urlencoded'
			},
			body: body.toString()
		});

		if (!response.ok) {
			const error = await response.text();
			throw new Error(`Token exchange failed: ${error}`);
		}

		return await response.json();
	}

	/**
	 * Get JWKS for token validation
	 */
	private getJWKS() {
		if (!jwksCache) {
			const jwksUri = `https://login.microsoftonline.com/${this.tenantId}/discovery/v2.0/keys`;
			jwksCache = createRemoteJWKSet(new URL(jwksUri));
		}
		return jwksCache;
	}

	/**
	 * Validate and decode JWT token
	 */
	async validateToken(token: string): Promise<AzureUser> {
		try {
			const jwks = this.getJWKS();
			const { payload } = await jwtVerify(token, jwks, {
				issuer: `https://login.microsoftonline.com/${this.tenantId}/v2.0`,
				audience: this.clientId
			});

			return {
				oid: payload.oid as string,
				name: payload.name as string,
				email: payload.email as string,
				preferred_username: payload.preferred_username as string,
				tid: payload.tid as string
			};
		} catch (error) {
			throw new Error(`Token validation failed: ${error}`);
		}
	}

	/**
	 * Create session cookie
	 */
	createSessionCookie(sessionData: SessionData): string {
		const sessionJson = JSON.stringify(sessionData);
		const sessionBase64 = btoa(sessionJson);

		// Determine security settings based on environment
		const isDevelopment = this.cookieDomain === 'localhost';
		const isProduction = !isDevelopment;

		return serialize('session', sessionBase64, {
			httpOnly: true,
			secure: isProduction, // Only secure in production (HTTPS required)
			sameSite: 'strict',
			maxAge: 60 * 60 * 24, // 24 hours
			path: '/',
			domain: isDevelopment ? undefined : this.cookieDomain
		});
	}

	/**
	 * Parse session from cookie
	 */
	parseSession(cookieHeader: string | null): SessionData | null {
		if (!cookieHeader) return null;

		try {
			const cookies = parse(cookieHeader);
			const sessionCookie = cookies.session;

			if (!sessionCookie) return null;

			const sessionJson = atob(sessionCookie);
			const sessionData: SessionData = JSON.parse(sessionJson);

			// Check if session is expired
			if (Date.now() > sessionData.expiresAt) {
				return null;
			}

			return sessionData;
		} catch {
			return null;
		}
	}

	/**
	 * Create logout cookie (clears session)
	 */
	createLogoutCookie(): string {
		const isDevelopment = this.cookieDomain === 'localhost';
		const isProduction = !isDevelopment;

		return serialize('session', '', {
			httpOnly: true,
			secure: isProduction, // Only secure in production (HTTPS required)
			sameSite: 'strict',
			maxAge: 0,
			path: '/',
			domain: isDevelopment ? undefined : this.cookieDomain
		});
	}

	/**
	 * Get Azure AD logout URL
	 */
	getLogoutUrl(): string {
		const params = new URLSearchParams({
			post_logout_redirect_uri: this.redirectUri.replace('/callback', '/login')
		});

		return `https://login.microsoftonline.com/${this.tenantId}/oauth2/v2.0/logout?${params}`;
	}
}

/**
 * Helper function to get authenticated user from request
 */
export async function getAuthenticatedUser(event: RequestEvent): Promise<AzureUser | null> {
	try {
		const authService = new AuthService(event.platform?.env || {});

		// Debug: Check all cookies
		const allCookies = event.request.headers.get('cookie');
		console.log('All cookies from request:', allCookies);

		// Use SvelteKit's cookies API instead of raw headers
		const sessionCookie = event.cookies.get('session');
		console.log('Session cookie from SvelteKit API:', sessionCookie ? 'found' : 'not found');

		if (!sessionCookie) {
			console.log('No session cookie found');
			return null;
		}

		// Parse the base64 encoded session data
		const sessionJson = atob(sessionCookie);
		const sessionData: SessionData = JSON.parse(sessionJson);

		// Check if session is expired
		if (Date.now() > sessionData.expiresAt) {
			console.log('Session expired');
			return null;
		}

		// Validate the token is still valid
		await authService.validateToken(sessionData.accessToken);
		console.log('User authenticated successfully:', sessionData.user.name);
		return sessionData.user;
	} catch (error) {
		console.log('Authentication error:', error);
		return null;
	}
}

/**
 * Helper function to require authentication
 */
export async function requireAuth(event: RequestEvent): Promise<AzureUser> {
	const user = await getAuthenticatedUser(event);
	
	if (!user) {
		throw redirect(302, '/login');
	}
	
	return user;
}
