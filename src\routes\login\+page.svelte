<script lang="ts">
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import { page } from '$app/stores';

	let loading = false;
	let error = '';

	onMount(() => {
		// Check for error in URL params
		const urlError = $page.url.searchParams.get('error');
		if (urlError) {
			error = 'Authentication failed. Please try again.';
		}
	});

	async function handleLogin() {
		loading = true;
		error = '';
		
		try {
			// Generate a random state parameter for CSRF protection
			const state = crypto.randomUUID();
			sessionStorage.setItem('oauth_state', state);
			
			// Redirect to Azure AD OAuth endpoint
			const response = await fetch('/api/auth/login', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({ state })
			});

			if (response.ok) {
				const { authUrl } = await response.json();
				window.location.href = authUrl;
			} else {
				throw new Error('Failed to initiate login');
			}
		} catch (err) {
			error = 'Failed to initiate login. Please try again.';
			loading = false;
		}
	}
</script>

<svelte:head>
	<title>Login - Azure AD SSO</title>
</svelte:head>

<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
	<div class="max-w-md w-full space-y-8">
		<div>
			<h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
				Sign in to your account
			</h2>
			<p class="mt-2 text-center text-sm text-gray-600">
				Use your Azure Active Directory credentials
			</p>
		</div>
		
		<div class="mt-8 space-y-6">
			{#if error}
				<div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative" role="alert">
					<span class="block sm:inline">{error}</span>
				</div>
			{/if}

			<div>
				<button
					type="button"
					on:click={handleLogin}
					disabled={loading}
					class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
				>
					{#if loading}
						<svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
							<circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
							<path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
						</svg>
						Signing in...
					{:else}
						Sign in with Azure AD
					{/if}
				</button>
			</div>

			<div class="text-center">
				<p class="text-xs text-gray-500">
					By signing in, you agree to our terms of service and privacy policy.
				</p>
			</div>
		</div>
	</div>
</div>

<style>
	/* Additional custom styles if needed */
</style>
