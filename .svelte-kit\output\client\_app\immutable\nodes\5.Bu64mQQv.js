import"../chunks/CWj6FrbW.js";import"../chunks/B4ZCArY7.js";import{o as B}from"../chunks/Dcy8ifye.js";import{p as E,f as S,t as x,a as d,b as O,$ as F,c as s,a0 as i,_ as h,s as y,B as p,r as a,n as w,a4 as I,a5 as L}from"../chunks/DfYgktsV.js";import{h as M,e as T,s as H}from"../chunks/DZXC8P3u.js";import{i as _}from"../chunks/_pqzL3p7.js";import{i as J}from"../chunks/BIhxBn-I.js";import{s as N,a as V}from"../chunks/BrXvW6T5.js";import{s as q}from"../chunks/BRLLTahE.js";const G=()=>{const r=q;return{page:{subscribe:r.page.subscribe},navigating:{subscribe:r.navigating.subscribe},updated:r.updated}},K={subscribe(r){return G().page.subscribe(r)}};var Q=S('<div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative" role="alert"><span class="block sm:inline"> </span></div>'),R=I('<svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg> Signing in...',1),W=S('<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8"><div class="max-w-md w-full space-y-8"><div><h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">Sign in to your account</h2> <p class="mt-2 text-center text-sm text-gray-600">Use your Azure Active Directory credentials</p></div> <div class="mt-8 space-y-6"><!> <div><button type="button" class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"><!></button></div> <div class="text-center"><p class="text-xs text-gray-500">By signing in, you agree to our terms of service and privacy policy.</p></div></div></div></div>');function it(r,g){E(g,!1);const[$,A]=N(),k=()=>V(K,"$page",$);let n=h(!1),o=h("");B(()=>{k().url.searchParams.get("error")&&i(o,"Authentication failed. Please try again.")});async function z(){i(n,!0),i(o,"");try{const t=crypto.randomUUID();sessionStorage.setItem("oauth_state",t);const e=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({state:t})});if(e.ok){const{authUrl:l}=await e.json();window.location.href=l}else throw new Error("Failed to initiate login")}catch{i(o,"Failed to initiate login. Please try again."),i(n,!1)}}J();var u=W();M(t=>{F.title="Login - Azure AD SSO"});var v=s(u),m=y(s(v),2),f=s(m);{var j=t=>{var e=Q(),l=s(e),U=s(l,!0);a(l),a(e),x(()=>H(U,p(o))),d(t,e)};_(f,t=>{p(o)&&t(j)})}var b=y(f,2),c=s(b),C=s(c);{var D=t=>{var e=R();w(),d(t,e)},P=t=>{var e=L("Sign in with Azure AD");d(t,e)};_(C,t=>{p(n)?t(D):t(P,!1)})}a(c),a(b),w(2),a(m),a(v),a(u),x(()=>c.disabled=p(n)),T("click",c,z),d(r,u),O(),A()}export{it as component};
