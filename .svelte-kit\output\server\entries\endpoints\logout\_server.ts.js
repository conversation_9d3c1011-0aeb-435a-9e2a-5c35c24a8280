import "@sveltejs/kit";
import { A as AuthService } from "../../../chunks/auth.js";
const POST = async ({ platform }) => {
  try {
    const env = platform?.env && Object.keys(platform.env).length > 0 ? platform.env : {
      AZURE_CLIENT_ID: process.env.AZURE_CLIENT_ID || "",
      AZURE_CLIENT_SECRET: process.env.AZURE_CLIENT_SECRET || "",
      AZURE_TENANT_ID: process.env.AZURE_TENANT_ID || "",
      REDIRECT_URI: process.env.REDIRECT_URI || "",
      SESSION_SECRET: process.env.SESSION_SECRET || "",
      COOKIE_DOMAIN: process.env.COOKIE_DOMAIN || "localhost"
    };
    const authService = new AuthService(env);
    const logoutCookie = authService.createLogoutCookie();
    const logoutUrl = authService.getLogoutUrl();
    return new Response(null, {
      status: 302,
      headers: {
        "Location": logoutUrl,
        "Set-Cookie": logoutCookie
      }
    });
  } catch (error) {
    console.error("Logout error:", error);
    const env = platform?.env && Object.keys(platform.env).length > 0 ? platform.env : {
      AZURE_CLIENT_ID: process.env.AZURE_CLIENT_ID || "",
      AZURE_CLIENT_SECRET: process.env.AZURE_CLIENT_SECRET || "",
      AZURE_TENANT_ID: process.env.AZURE_TENANT_ID || "",
      REDIRECT_URI: process.env.REDIRECT_URI || "",
      SESSION_SECRET: process.env.SESSION_SECRET || "",
      COOKIE_DOMAIN: process.env.COOKIE_DOMAIN || "localhost"
    };
    const authService = new AuthService(env);
    const logoutCookie = authService.createLogoutCookie();
    return new Response(null, {
      status: 302,
      headers: {
        "Location": "/login",
        "Set-Cookie": logoutCookie
      }
    });
  }
};
const GET = async ({ platform }) => {
  const env = platform?.env && Object.keys(platform.env).length > 0 ? platform.env : {
    AZURE_CLIENT_ID: process.env.AZURE_CLIENT_ID || "",
    AZURE_CLIENT_SECRET: process.env.AZURE_CLIENT_SECRET || "",
    AZURE_TENANT_ID: process.env.AZURE_TENANT_ID || "",
    REDIRECT_URI: process.env.REDIRECT_URI || "",
    SESSION_SECRET: process.env.SESSION_SECRET || "",
    COOKIE_DOMAIN: process.env.COOKIE_DOMAIN || "localhost"
  };
  const authService = new AuthService(env);
  const logoutCookie = authService.createLogoutCookie();
  return new Response(null, {
    status: 302,
    headers: {
      "Location": "/login",
      "Set-Cookie": logoutCookie
    }
  });
};
export {
  GET,
  POST
};
