import "@sveltejs/kit";
import { A as AuthService } from "../../../chunks/auth.js";
const POST = async ({ platform }) => {
  try {
    const authService = new AuthService(platform?.env || {});
    const logoutCookie = authService.createLogoutCookie();
    const logoutUrl = authService.getLogoutUrl();
    return new Response(null, {
      status: 302,
      headers: {
        "Location": logoutUrl,
        "Set-Cookie": logoutCookie
      }
    });
  } catch (error) {
    console.error("Logout error:", error);
    const authService = new AuthService(platform?.env || {});
    const logoutCookie = authService.createLogoutCookie();
    return new Response(null, {
      status: 302,
      headers: {
        "Location": "/login",
        "Set-Cookie": logoutCookie
      }
    });
  }
};
const GET = async ({ platform }) => {
  const authService = new AuthService(platform?.env || {});
  const logoutCookie = authService.createLogoutCookie();
  return new Response(null, {
    status: 302,
    headers: {
      "Location": "/login",
      "Set-Cookie": logoutCookie
    }
  });
};
export {
  GET,
  POST
};
