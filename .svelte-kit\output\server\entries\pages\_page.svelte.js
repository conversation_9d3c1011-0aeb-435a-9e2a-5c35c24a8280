import { y as head, z as bind_props } from "../../chunks/index.js";
import { e as escape_html } from "../../chunks/escaping.js";
function _page($$payload, $$props) {
  let data = $$props["data"];
  const { user } = data;
  head($$payload, ($$payload2) => {
    $$payload2.title = `<title>Home - Azure AD SSO</title>`;
  });
  $$payload.out += `<div class="min-h-screen bg-gray-50"><nav class="bg-white shadow"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="flex justify-between h-16"><div class="flex items-center"><h1 class="text-xl font-semibold text-gray-900">Home</h1></div> <div class="flex items-center space-x-4"><a href="/dashboard" class="text-blue-600 hover:text-blue-800 px-3 py-2 rounded-md text-sm font-medium">Dashboard</a> <span class="text-sm text-gray-700">Welcome, ${escape_html(user.name)}</span> <button type="button" class="bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded-md text-sm font-medium">Logout</button></div></div></div></nav> <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8"><div class="px-4 py-6 sm:px-0"><div class="border-4 border-dashed border-gray-200 rounded-lg p-8"><div class="text-center"><h2 class="text-3xl font-bold text-gray-900 mb-4">Welcome to Azure AD SSO App</h2> <p class="text-gray-600 mb-8">You have successfully authenticated with Azure Active Directory and can access the home page.</p> <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 max-w-4xl mx-auto"><a href="/dashboard" class="bg-blue-600 hover:bg-blue-700 text-white p-6 rounded-lg shadow transition-colors"><div class="text-center"><svg class="h-8 w-8 mx-auto mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path></svg> <h3 class="font-semibold">Dashboard</h3> <p class="text-sm opacity-90">View detailed user information</p></div></a> <div class="bg-green-600 text-white p-6 rounded-lg shadow"><div class="text-center"><svg class="h-8 w-8 mx-auto mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg> <h3 class="font-semibold">Authenticated</h3> <p class="text-sm opacity-90">JWT token validated</p></div></div> <div class="bg-purple-600 text-white p-6 rounded-lg shadow"><div class="text-center"><svg class="h-8 w-8 mx-auto mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path></svg> <h3 class="font-semibold">Cloudflare Workers</h3> <p class="text-sm opacity-90">Running on the edge</p></div></div></div> <div class="mt-8 bg-white rounded-lg shadow p-6 max-w-md mx-auto"><h3 class="text-lg font-medium text-gray-900 mb-4">Current User</h3> <div class="text-left space-y-2"><p class="text-sm"><span class="font-medium text-gray-500">Name:</span> ${escape_html(user.name)}</p> <p class="text-sm"><span class="font-medium text-gray-500">Email:</span> ${escape_html(user.email)}</p> <p class="text-sm"><span class="font-medium text-gray-500">Username:</span> ${escape_html(user.preferred_username)}</p></div></div></div></div></div></main></div>`;
  bind_props($$props, { data });
}
export {
  _page as default
};
