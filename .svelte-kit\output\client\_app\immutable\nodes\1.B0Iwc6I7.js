import"../chunks/CWj6FrbW.js";import"../chunks/BrFt5aUl.js";import{a2 as h,f as g,a1 as l,t as v,a as d,a3 as _,c as e,r as o,s as x}from"../chunks/-lpZNR2G.js";import{s as p}from"../chunks/oU013O0A.js";import{i as $}from"../chunks/_QLPfm2f.js";import{s as k,p as m}from"../chunks/9V84_5nK.js";const b={get error(){return m.error},get status(){return m.status}};k.updated.check;const i=b;var E=g("<h1> </h1> <p> </p>",1);function B(c,f){h(f,!1),$();var t=E(),r=l(t),n=e(r,!0);o(r);var a=x(r,2),u=e(a,!0);o(a),v(()=>{var s;p(n,i.status),p(u,(s=i.error)==null?void 0:s.message)}),d(c,t),_()}export{B as component};
