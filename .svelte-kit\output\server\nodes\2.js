import * as server from '../entries/pages/_page.server.ts.js';

export const index = 2;
let component_cache;
export const component = async () => component_cache ??= (await import('../entries/pages/_page.svelte.js')).default;
export { server };
export const server_id = "src/routes/+page.server.ts";
export const imports = ["_app/immutable/nodes/2.DZ_2S7Io.js","_app/immutable/chunks/CWj6FrbW.js","_app/immutable/chunks/BrFt5aUl.js","_app/immutable/chunks/-lpZNR2G.js","_app/immutable/chunks/oU013O0A.js","_app/immutable/chunks/pe1MSJR9.js","_app/immutable/chunks/DeiEKfcT.js"];
export const stylesheets = [];
export const fonts = [];
