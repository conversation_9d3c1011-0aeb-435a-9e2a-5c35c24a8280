

export const index = 2;
let component_cache;
export const component = async () => component_cache ??= (await import('../entries/pages/_page.svelte.js')).default;
export const imports = ["_app/immutable/nodes/2.J7LDJ7_1.js","_app/immutable/chunks/CWj6FrbW.js","_app/immutable/chunks/B4ZCArY7.js","_app/immutable/chunks/DfYgktsV.js","_app/immutable/chunks/Dcy8ifye.js","_app/immutable/chunks/DZXC8P3u.js","_app/immutable/chunks/imUBLVoF.js","_app/immutable/chunks/BIhxBn-I.js","_app/immutable/chunks/BRLLTahE.js"];
export const stylesheets = [];
export const fonts = [];
