import { redirect } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';
import { AuthService, type SessionData } from '$lib/auth';

export const load: PageServerLoad = async ({ url, platform, setHeaders }) => {
	const code = url.searchParams.get('code');
	const state = url.searchParams.get('state');
	const error = url.searchParams.get('error');

	// Handle OAuth errors
	if (error) {
		console.error('OAuth error:', error);
		throw redirect(302, '/login?error=oauth_error');
	}

	// Validate required parameters
	if (!code || !state) {
		console.error('Missing code or state parameter');
		throw redirect(302, '/login?error=missing_params');
	}

	try {
		// Get environment variables with fallback
		const env = platform?.env && Object.keys(platform.env).length > 0
			? platform.env
			: {
				AZURE_CLIENT_ID: process.env.AZURE_CLIENT_ID || '',
				AZURE_CLIENT_SECRET: process.env.AZURE_CLIENT_SECRET || '',
				AZURE_TENANT_ID: process.env.AZURE_TENANT_ID || '',
				REDIRECT_URI: process.env.REDIRECT_URI || '',
				SESSION_SECRET: process.env.SESSION_SECRET || '',
				COOKIE_DOMAIN: process.env.COOKIE_DOMAIN || 'localhost'
			};

		const authService = new AuthService(env);

		// Exchange authorization code for tokens
		const tokenResponse = await authService.exchangeCodeForToken(code);
		
		if (!tokenResponse.access_token || !tokenResponse.id_token) {
			throw new Error('Invalid token response');
		}

		// Validate and decode the ID token to get user info
		const user = await authService.validateToken(tokenResponse.id_token);

		// Create session data
		const sessionData: SessionData = {
			user,
			accessToken: tokenResponse.access_token,
			expiresAt: Date.now() + (60 * 60 * 1000) // 1 hour from now
		};

		// Create session cookie
		const sessionCookie = authService.createSessionCookie(sessionData);

		// Set the cookie header and redirect to root
		setHeaders({
			'Set-Cookie': sessionCookie
		});

		throw redirect(302, '/');

	} catch (error) {
		console.error('Callback processing error:', error);
		throw redirect(302, '/login?error=callback_error');
	}
};
