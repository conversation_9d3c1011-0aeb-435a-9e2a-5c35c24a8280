import { redirect } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';
import { AuthService, type SessionData } from '$lib/auth';

export const load: PageServerLoad = async ({ url, platform, cookies }) => {
	console.log('Callback handler called with URL:', url.href);

	const code = url.searchParams.get('code');
	const state = url.searchParams.get('state');
	const error = url.searchParams.get('error');

	console.log('Callback parameters:', { code: code ? 'present' : 'missing', state: state ? 'present' : 'missing', error });

	// Handle OAuth errors
	if (error) {
		console.error('OAuth error:', error);
		throw redirect(302, '/login?error=oauth_error');
	}

	// Validate required parameters
	if (!code || !state) {
		console.error('Missing code or state parameter');
		throw redirect(302, '/login?error=missing_params');
	}

	try {
		const authService = new AuthService(platform?.env || {});

		// Exchange authorization code for tokens
		const tokenResponse = await authService.exchangeCodeForToken(code);
		
		if (!tokenResponse.access_token || !tokenResponse.id_token) {
			throw new Error('Invalid token response');
		}

		// Validate and decode the ID token to get user info
		const user = await authService.validateToken(tokenResponse.id_token);

		// Create session data
		const sessionData: SessionData = {
			user,
			accessToken: tokenResponse.access_token,
			expiresAt: Date.now() + (60 * 60 * 1000) // 1 hour from now
		};

		// Set session cookie using SvelteKit's cookie API
		const env = platform?.env || {};
		const cookieDomain = env.COOKIE_DOMAIN || 'localhost';
		const environment = env.ENVIRONMENT || 'develop';

		// For development: allow HTTP, for production: require HTTPS
		const isProduction = environment === 'production';
		const isDevelopment = environment === 'develop' || cookieDomain === 'localhost';

		// Encode session data as base64 like the original implementation
		const sessionJson = JSON.stringify(sessionData);
		const sessionBase64 = btoa(sessionJson);

		cookies.set('session', sessionBase64, {
			httpOnly: true,
			secure: isProduction, // Only secure in production
			sameSite: 'strict',
			maxAge: 60 * 60 * 24, // 24 hours
			path: '/',
			domain: isDevelopment ? undefined : cookieDomain
		});

		console.log('Session cookie set successfully, redirecting to home page');
		throw redirect(302, '/');

	} catch (error) {
		// If it's a redirect, let it through (this is normal SvelteKit behavior)
		if (error instanceof Response && error.status >= 300 && error.status < 400) {
			throw error;
		}

		console.error('Callback processing error:', error);
		throw redirect(302, '/login?error=callback_error');
	}
};
