

export const index = 1;
let component_cache;
export const component = async () => component_cache ??= (await import('../entries/fallbacks/error.svelte.js')).default;
export const imports = ["_app/immutable/nodes/1.B0Iwc6I7.js","_app/immutable/chunks/CWj6FrbW.js","_app/immutable/chunks/BrFt5aUl.js","_app/immutable/chunks/-lpZNR2G.js","_app/immutable/chunks/oU013O0A.js","_app/immutable/chunks/_QLPfm2f.js","_app/immutable/chunks/9V84_5nK.js","_app/immutable/chunks/C1oTikCN.js","_app/immutable/chunks/DHFUQ1My.js"];
export const stylesheets = [];
export const fonts = [];
