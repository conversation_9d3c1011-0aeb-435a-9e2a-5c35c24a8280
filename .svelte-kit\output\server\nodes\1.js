

export const index = 1;
let component_cache;
export const component = async () => component_cache ??= (await import('../entries/fallbacks/error.svelte.js')).default;
export const imports = ["_app/immutable/nodes/1.CJDWMOzc.js","_app/immutable/chunks/CWj6FrbW.js","_app/immutable/chunks/B4ZCArY7.js","_app/immutable/chunks/DfYgktsV.js","_app/immutable/chunks/DZXC8P3u.js","_app/immutable/chunks/BIhxBn-I.js","_app/immutable/chunks/BRLLTahE.js","_app/immutable/chunks/Dcy8ifye.js","_app/immutable/chunks/imUBLVoF.js"];
export const stylesheets = [];
export const fonts = [];
