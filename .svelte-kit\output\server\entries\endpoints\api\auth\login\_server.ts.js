import { json } from "@sveltejs/kit";
import { A as AuthService } from "../../../../../chunks/auth.js";
const POST = async ({ request, platform }) => {
  try {
    const { state } = await request.json();
    if (!state) {
      return json({ error: "State parameter is required" }, { status: 400 });
    }
    const authService = new AuthService(platform?.env || {});
    const authUrl = authService.getAuthUrl(state);
    return json({ authUrl });
  } catch (error) {
    console.error("Login API error:", error);
    return json({ error: "Internal server error" }, { status: 500 });
  }
};
export {
  POST
};
