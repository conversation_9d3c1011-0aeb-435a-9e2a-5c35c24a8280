import{b as c,g as l}from"./DZXC8P3u.js";import{V as o,Z as b,W as f,_ as p,B as d,a0 as _}from"./DfYgktsV.js";let s=!1,i=Symbol();function y(e,n,r){const u=r[n]??(r[n]={store:null,source:p(void 0),unsubscribe:f});if(u.store!==e&&!(i in r))if(u.unsubscribe(),u.store=e??null,e==null)u.source.v=void 0,u.unsubscribe=f;else{var t=!0;u.unsubscribe=c(e,a=>{t?u.source.v=a:_(u.source,a)}),t=!1}return e&&i in r?l(e):d(u.source)}function m(){const e={};function n(){o(()=>{for(var r in e)e[r].unsubscribe();b(e,i,{enumerable:!1,value:!0})})}return[e,n]}function N(e){var n=s;try{return s=!1,[e(),s]}finally{s=n}}export{y as a,N as c,m as s};
