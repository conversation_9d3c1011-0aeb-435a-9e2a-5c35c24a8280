import"../chunks/CWj6FrbW.js";import"../chunks/BrFt5aUl.js";import{f as E,t as H,a as J,$ as N,s as e,c as t,r as s,n as Y,u as a}from"../chunks/-lpZNR2G.js";import{h as q,e as F,s as r}from"../chunks/oU013O0A.js";import{p as G}from"../chunks/pe1MSJR9.js";var K=E('<div class="min-h-screen bg-gray-50"><nav class="bg-white shadow"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="flex justify-between h-16"><div class="flex items-center"><h1 class="text-xl font-semibold text-gray-900">Dashboard</h1></div> <div class="flex items-center space-x-4"><span class="text-sm text-gray-700"> </span> <button type="button" class="bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded-md text-sm font-medium">Logout</button></div></div></div></nav> <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8"><div class="px-4 py-6 sm:px-0"><div class="border-4 border-dashed border-gray-200 rounded-lg p-8"><div class="text-center"><h2 class="text-2xl font-bold text-gray-900 mb-4">Welcome to your Dashboard</h2> <p class="text-gray-600 mb-8">You have successfully authenticated with Azure Active Directory.</p> <div class="bg-white overflow-hidden shadow rounded-lg max-w-md mx-auto"><div class="px-4 py-5 sm:p-6"><h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">User Information</h3> <dl class="grid grid-cols-1 gap-x-4 gap-y-4 sm:grid-cols-2"><div class="sm:col-span-2"><dt class="text-sm font-medium text-gray-500">Name</dt> <dd class="mt-1 text-sm text-gray-900"> </dd></div> <div class="sm:col-span-2"><dt class="text-sm font-medium text-gray-500">Email</dt> <dd class="mt-1 text-sm text-gray-900"> </dd></div> <div class="sm:col-span-2"><dt class="text-sm font-medium text-gray-500">Username</dt> <dd class="mt-1 text-sm text-gray-900"> </dd></div> <div class="sm:col-span-2"><dt class="text-sm font-medium text-gray-500">User ID</dt> <dd class="mt-1 text-sm text-gray-900 font-mono text-xs"> </dd></div> <div class="sm:col-span-2"><dt class="text-sm font-medium text-gray-500">Tenant ID</dt> <dd class="mt-1 text-sm text-gray-900 font-mono text-xs"> </dd></div></dl></div></div> <div class="mt-8 grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3"><div class="bg-white overflow-hidden shadow rounded-lg"><div class="p-5"><div class="flex items-center"><div class="flex-shrink-0"><svg class="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg></div> <div class="ml-5 w-0 flex-1"><dl><dt class="text-sm font-medium text-gray-500 truncate">Authentication Status</dt> <dd class="text-lg font-medium text-gray-900">Authenticated</dd></dl></div></div></div></div> <div class="bg-white overflow-hidden shadow rounded-lg"><div class="p-5"><div class="flex items-center"><div class="flex-shrink-0"><svg class="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path></svg></div> <div class="ml-5 w-0 flex-1"><dl><dt class="text-sm font-medium text-gray-500 truncate">Security</dt> <dd class="text-lg font-medium text-gray-900">JWT Validated</dd></dl></div></div></div></div> <div class="bg-white overflow-hidden shadow rounded-lg"><div class="p-5"><div class="flex items-center"><div class="flex-shrink-0"><svg class="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path></svg></div> <div class="ml-5 w-0 flex-1"><dl><dt class="text-sm font-medium text-gray-500 truncate">Platform</dt> <dd class="text-lg font-medium text-gray-900">Cloudflare Workers</dd></dl></div></div></div></div></div></div></div></div></main></div>');function st(C,L){let W=G(L,"data",8);const{user:d}=W();async function B(){try{(await fetch("/logout",{method:"POST"})).ok&&(window.location.href="/login")}catch(x){console.error("Logout error:",x)}}var i=K();q(x=>{N.title="Dashboard - Azure AD SSO"});var l=t(i),g=t(l),u=t(g),h=e(t(u),2),o=t(h),I=t(o);s(o);var M=e(o,2);s(h),s(u),s(g),s(l);var p=e(l,2),f=t(p),y=t(f),w=t(y),_=e(t(w),4),b=t(_),k=e(t(b),2),v=t(k),D=e(t(v),2),T=t(D,!0);s(D),s(v);var n=e(v,2),z=e(t(n),2),U=t(z,!0);s(z),s(n);var c=e(n,2),A=e(t(c),2),V=t(A,!0);s(A),s(c);var m=e(c,2),S=e(t(m),2),O=t(S,!0);s(S),s(m);var $=e(m,2),j=e(t($),2),P=t(j,!0);s(j),s($),s(k),s(b),s(_),Y(2),s(w),s(y),s(f),s(p),s(i),H(()=>{r(I,`Welcome, ${a(()=>d.name)??""}`),r(T,a(()=>d.name)),r(U,a(()=>d.email)),r(V,a(()=>d.preferred_username)),r(O,a(()=>d.oid)),r(P,a(()=>d.tid))}),F("click",M,B),J(C,i)}export{st as component};
