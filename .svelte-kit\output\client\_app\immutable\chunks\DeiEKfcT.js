import{b as c,g as l}from"./oU013O0A.js";import{S as o,X as b,T as a,Y as p,z as d,Z as _}from"./-lpZNR2G.js";let s=!1,i=Symbol();function y(e,n,r){const u=r[n]??(r[n]={store:null,source:p(void 0),unsubscribe:a});if(u.store!==e&&!(i in r))if(u.unsubscribe(),u.store=e??null,e==null)u.source.v=void 0,u.unsubscribe=a;else{var t=!0;u.unsubscribe=c(e,f=>{t?u.source.v=f:_(u.source,f)}),t=!1}return e&&i in r?l(e):d(u.source)}function m(){const e={};function n(){o(()=>{for(var r in e)e[r].unsubscribe();b(e,i,{enumerable:!1,value:!0})})}return[e,n]}function S(e){var n=s;try{return s=!1,[e(),s]}finally{s=n}}export{y as a,S as c,m as s};
