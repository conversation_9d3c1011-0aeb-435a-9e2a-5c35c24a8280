import{at as m,au as T,z as o,av as B,Z as D,aw as L,a8 as z,ax as M,ay as Y,J as x,az as y,aA as N,ap as U,aq as h,B as j,u as q,aB as w,aC as C,aD as Z}from"./-lpZNR2G.js";import{c as $}from"./DeiEKfcT.js";function F(a,r,i,s){var I;var f=!x||(i&y)!==0,v=(i&N)!==0,A=(i&Z)!==0,n=s,c=!0,P=()=>(c&&(c=!1,n=A?q(s):s),n),u;if(v){var E=U in a||h in a;u=((I=m(a,r))==null?void 0:I.set)??(E&&r in a?e=>a[r]=e:void 0)}var _,g=!1;v?[_,g]=$(()=>a[r]):_=a[r],_===void 0&&s!==void 0&&(_=P(),u&&(f&&w(),u(_)));var t;if(f?t=()=>{var e=a[r];return e===void 0?P():(c=!0,e)}:t=()=>{var e=a[r];return e!==void 0&&(n=void 0),e===void 0?n:e},f&&(i&T)===0)return t;if(u){var R=a.$$legacy;return function(e,l){return arguments.length>0?((!f||!l||R||g)&&u(l?t():e),e):t()}}var S=!1,d=((i&C)!==0?j:Y)(()=>(S=!1,t()));v&&o(d);var b=z;return function(e,l){if(arguments.length>0){const O=l?o(d):f&&v?B(e):e;return D(d,O),S=!0,n!==void 0&&(n=O),e}return L&&S||(b.f&M)!==0?d.v:o(d)}}const H=Object.freeze(Object.defineProperty({__proto__:null},Symbol.toStringTag,{value:"Module"}));export{H as _,F as p};
