import{b as R,h as u,d as g,E as h,e as S,H as k,g as D,i as H,j as F,k as I,l as b,m as _,p as m,U as L,o as O}from"./-lpZNR2G.js";function Y(v,A,[t,s]=[0,0]){u&&t===0&&g();var a=v,f=null,e=null,i=L,E=t>0?h:0,n=!1;const p=(c,l=!0)=>{n=!0,o(l,c)},o=(c,l)=>{if(i===(i=c))return;let T=!1;if(u&&s!==-1){if(t===0){const r=S(a);r===k?s=0:r===D?s=1/0:(s=parseInt(r.substring(1)),s!==s&&(s=i?1/0:-1))}const N=s>t;!!i===N&&(a=H(),F(a),I(!1),T=!0,s=-1)}i?(f?b(f):l&&(f=_(()=>l(a))),e&&m(e,()=>{e=null})):(e?b(e):l&&(e=_(()=>l(a,[t+1,s]))),f&&m(f,()=>{f=null})),T&&I(!0)};R(()=>{n=!1,A(p),n||o(null,null)},E),u&&(a=O)}export{Y as i};
