import { y as head, w as pop, u as push } from "../../../chunks/index.js";
import "../../../chunks/client.js";
import { e as escape_html } from "../../../chunks/escaping.js";
import "clsx";
const replacements = {
  translate: /* @__PURE__ */ new Map([
    [true, "yes"],
    [false, "no"]
  ])
};
function attr(name, value, is_boolean = false) {
  if (is_boolean) return "";
  const normalized = name in replacements && replacements[name].get(value) || value;
  const assignment = is_boolean ? "" : `="${escape_html(normalized, true)}"`;
  return ` ${name}${assignment}`;
}
function _page($$payload, $$props) {
  push();
  let loading = false;
  head($$payload, ($$payload2) => {
    $$payload2.title = `<title>Login - Azure AD SSO</title>`;
  });
  $$payload.out += `<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8"><div class="max-w-md w-full space-y-8"><div><h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">Sign in to your account</h2> <p class="mt-2 text-center text-sm text-gray-600">Use your Azure Active Directory credentials</p></div> <div class="mt-8 space-y-6">`;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> <div><button type="button"${attr("disabled", loading, true)} class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed">`;
  {
    $$payload.out += "<!--[!-->";
    $$payload.out += `Sign in with Azure AD`;
  }
  $$payload.out += `<!--]--></button></div> <div class="text-center"><p class="text-xs text-gray-500">By signing in, you agree to our terms of service and privacy policy.</p></div></div></div></div>`;
  pop();
}
export {
  _page as default
};
