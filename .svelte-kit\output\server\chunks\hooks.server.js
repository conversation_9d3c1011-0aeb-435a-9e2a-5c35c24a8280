import { redirect } from "@sveltejs/kit";
import { g as getAuthenticatedUser } from "./auth.js";
const protectedRoutes = ["/", "/dashboard"];
const publicOnlyRoutes = ["/login"];
const handle = async ({ event, resolve }) => {
  const response = await resolve(event, {
    transformPageChunk: ({ html }) => html
  });
  response.headers.set("X-Frame-Options", "DENY");
  response.headers.set("X-Content-Type-Options", "nosniff");
  response.headers.set("Referrer-Policy", "strict-origin-when-cross-origin");
  response.headers.set("Permissions-Policy", "camera=(), microphone=(), geolocation=()");
  const csp = [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline'",
    "style-src 'self' 'unsafe-inline'",
    "img-src 'self' data: https:",
    "font-src 'self'",
    "connect-src 'self' https://login.microsoftonline.com",
    "frame-ancestors 'none'",
    "base-uri 'self'",
    "form-action 'self' https://login.microsoftonline.com"
  ].join("; ");
  response.headers.set("Content-Security-Policy", csp);
  if (event.url.protocol === "https:") {
    response.headers.set("Strict-Transport-Security", "max-age=31536000; includeSubDomains");
  }
  const pathname = event.url.pathname;
  if (pathname.startsWith("/callback") || pathname.startsWith("/logout") || pathname.startsWith("/api/") || pathname.startsWith("/_app/") || pathname.includes(".")) {
    return response;
  }
  try {
    const user = await getAuthenticatedUser(event);
    if (user && publicOnlyRoutes.includes(pathname)) {
      throw redirect(302, "/dashboard");
    }
    if (!user && protectedRoutes.includes(pathname)) {
      throw redirect(302, "/login");
    }
    event.locals.user = user || void 0;
  } catch (error) {
    if (error instanceof Response && error.status >= 300 && error.status < 400) {
      throw error;
    }
    if (protectedRoutes.includes(pathname)) {
      throw redirect(302, "/login");
    }
  }
  return response;
};
export {
  handle
};
