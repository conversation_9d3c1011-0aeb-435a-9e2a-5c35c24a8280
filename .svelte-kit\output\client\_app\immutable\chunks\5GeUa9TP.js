import{at as m,au as D,B as o,av as L,a0 as T,aw as B,a8 as M,ax as Y,ay as x,L as y,az as z,aA as N,ap as U,aq as h,D as j,u as q,aB as w,aC as C,aD as $}from"./DfYgktsV.js";import{c as G}from"./BrXvW6T5.js";function H(a,r,i,s){var I;var f=!y||(i&z)!==0,v=(i&N)!==0,A=(i&$)!==0,n=s,c=!0,P=()=>(c&&(c=!1,n=A?q(s):s),n),u;if(v){var E=U in a||h in a;u=((I=m(a,r))==null?void 0:I.set)??(E&&r in a?e=>a[r]=e:void 0)}var _,g=!1;v?[_,g]=G(()=>a[r]):_=a[r],_===void 0&&s!==void 0&&(_=P(),u&&(f&&w(),u(_)));var t;if(f?t=()=>{var e=a[r];return e===void 0?P():(c=!0,e)}:t=()=>{var e=a[r];return e!==void 0&&(n=void 0),e===void 0?n:e},f&&(i&D)===0)return t;if(u){var R=a.$$legacy;return function(e,l){return arguments.length>0?((!f||!l||R||g)&&u(l?t():e),e):t()}}var S=!1,d=((i&C)!==0?j:x)(()=>(S=!1,t()));v&&o(d);var b=M;return function(e,l){if(arguments.length>0){const O=l?o(d):f&&v?L(e):e;return T(d,O),S=!0,n!==void 0&&(n=O),e}return B&&S||(b.f&Y)!==0?d.v:o(d)}}const J=Object.freeze(Object.defineProperty({__proto__:null},Symbol.toStringTag,{value:"Module"}));export{J as _,H as p};
