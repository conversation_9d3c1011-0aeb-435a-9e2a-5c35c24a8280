import { redirect } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { AuthService } from '$lib/auth';

export const POST: RequestHandler = async ({ platform, cookies }) => {
	try {
		const authService = new AuthService(platform?.env || {});

		// Clear session cookie using SvelteKit's cookie API
		cookies.delete('session', { path: '/' });

		// Get Azure AD logout URL
		const logoutUrl = authService.getLogoutUrl();

		// Redirect to Azure AD logout
		return new Response(null, {
			status: 302,
			headers: {
				'Location': logoutUrl
			}
		});
	} catch (error) {
		console.error('Logout error:', error);

		// Fallback: clear cookie and redirect to login
		cookies.delete('session', { path: '/' });

		return new Response(null, {
			status: 302,
			headers: {
				'Location': '/login'
			}
		});
	}
};

export const GET: RequestHandler = async ({ platform, cookies }) => {
	// Handle GET requests (e.g., from Azure AD logout redirect)
	cookies.delete('session', { path: '/' });

	return new Response(null, {
		status: 302,
		headers: {
			'Location': '/login'
		}
	});
};
