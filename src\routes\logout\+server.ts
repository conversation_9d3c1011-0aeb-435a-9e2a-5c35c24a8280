import { redirect } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { AuthService } from '$lib/auth';

export const POST: RequestHandler = async ({ platform }) => {
	try {
		// Get environment variables with fallback
		const env = platform?.env && Object.keys(platform.env).length > 0
			? platform.env
			: {
				AZURE_CLIENT_ID: process.env.AZURE_CLIENT_ID || '',
				AZURE_CLIENT_SECRET: process.env.AZURE_CLIENT_SECRET || '',
				AZURE_TENANT_ID: process.env.AZURE_TENANT_ID || '',
				REDIRECT_URI: process.env.REDIRECT_URI || '',
				SESSION_SECRET: process.env.SESSION_SECRET || '',
				COOKIE_DOMAIN: process.env.COOKIE_DOMAIN || 'localhost'
			};

		const authService = new AuthService(env);
		
		// Create logout cookie (clears session)
		const logoutCookie = authService.createLogoutCookie();
		
		// Get Azure AD logout URL
		const logoutUrl = authService.getLogoutUrl();
		
		// Return response with cleared cookie and redirect to Azure AD logout
		return new Response(null, {
			status: 302,
			headers: {
				'Location': logoutUrl,
				'Set-Cookie': logoutCookie
			}
		});
	} catch (error) {
		console.error('Logout error:', error);
		
		// Fallback: clear cookie and redirect to login
		const env = platform?.env && Object.keys(platform.env).length > 0
			? platform.env
			: {
				AZURE_CLIENT_ID: process.env.AZURE_CLIENT_ID || '',
				AZURE_CLIENT_SECRET: process.env.AZURE_CLIENT_SECRET || '',
				AZURE_TENANT_ID: process.env.AZURE_TENANT_ID || '',
				REDIRECT_URI: process.env.REDIRECT_URI || '',
				SESSION_SECRET: process.env.SESSION_SECRET || '',
				COOKIE_DOMAIN: process.env.COOKIE_DOMAIN || 'localhost'
			};

		const authService = new AuthService(env);
		const logoutCookie = authService.createLogoutCookie();
		
		return new Response(null, {
			status: 302,
			headers: {
				'Location': '/login',
				'Set-Cookie': logoutCookie
			}
		});
	}
};

export const GET: RequestHandler = async ({ platform }) => {
	// Handle GET requests (e.g., from Azure AD logout redirect)
	const env = platform?.env && Object.keys(platform.env).length > 0
		? platform.env
		: {
			AZURE_CLIENT_ID: process.env.AZURE_CLIENT_ID || '',
			AZURE_CLIENT_SECRET: process.env.AZURE_CLIENT_SECRET || '',
			AZURE_TENANT_ID: process.env.AZURE_TENANT_ID || '',
			REDIRECT_URI: process.env.REDIRECT_URI || '',
			SESSION_SECRET: process.env.SESSION_SECRET || '',
			COOKIE_DOMAIN: process.env.COOKIE_DOMAIN || 'localhost'
		};

	const authService = new AuthService(env);
	const logoutCookie = authService.createLogoutCookie();
	
	return new Response(null, {
		status: 302,
		headers: {
			'Location': '/login',
			'Set-Cookie': logoutCookie
		}
	});
};
