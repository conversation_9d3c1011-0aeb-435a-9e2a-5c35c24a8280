import { redirect } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { AuthService } from '$lib/auth';

export const POST: RequestHandler = async ({ platform }) => {
	try {
		const authService = new AuthService(platform?.env || {});
		
		// Create logout cookie (clears session)
		const logoutCookie = authService.createLogoutCookie();
		
		// Get Azure AD logout URL
		const logoutUrl = authService.getLogoutUrl();
		
		// Return response with cleared cookie and redirect to Azure AD logout
		return new Response(null, {
			status: 302,
			headers: {
				'Location': logoutUrl,
				'Set-Cookie': logoutCookie
			}
		});
	} catch (error) {
		console.error('Logout error:', error);
		
		// Fallback: clear cookie and redirect to login
		const authService = new AuthService(platform?.env || {});
		const logoutCookie = authService.createLogoutCookie();
		
		return new Response(null, {
			status: 302,
			headers: {
				'Location': '/login',
				'Set-Cookie': logoutCookie
			}
		});
	}
};

export const GET: RequestHandler = async ({ platform }) => {
	// Handle GET requests (e.g., from Azure AD logout redirect)
	const authService = new AuthService(platform?.env || {});
	const logoutCookie = authService.createLogoutCookie();
	
	return new Response(null, {
		status: 302,
		headers: {
			'Location': '/login',
			'Set-Cookie': logoutCookie
		}
	});
};
