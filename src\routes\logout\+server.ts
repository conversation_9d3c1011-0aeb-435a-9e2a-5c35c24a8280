import { redirect } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { AuthService } from '$lib/auth';

export const POST: RequestHandler = async ({ platform, cookies }) => {
	try {
		const authService = new AuthService(platform?.env || {});

		// Clear session cookie using SvelteKit's cookie API
		cookies.delete('session', { path: '/' });

		// Get Azure AD logout URL
		const logoutUrl = authService.getLogoutUrl();

		// Return JSON response with logout URL for frontend to handle
		return new Response(JSON.stringify({
			success: true,
			logoutUrl
		}), {
			status: 200,
			headers: {
				'Content-Type': 'application/json'
			}
		});
	} catch (error) {
		console.error('Logout error:', error);

		// Fallback: clear cookie and return login URL
		cookies.delete('session', { path: '/' });

		return new Response(JSON.stringify({
			success: true,
			logoutUrl: '/login'
		}), {
			status: 200,
			headers: {
				'Content-Type': 'application/json'
			}
		});
	}
};

export const GET: RequestHandler = async ({ platform, cookies }) => {
	// Handle GET requests (e.g., from Azure AD logout redirect)
	cookies.delete('session', { path: '/' });

	return new Response(null, {
		status: 302,
		headers: {
			'Location': '/login'
		}
	});
};
