{"clientTcpRtt": 13, "requestHeaderNames": {}, "httpProtocol": "HTTP/1.1", "tlsCipher": "AEAD-AES256-GCM-SHA384", "continent": "SA", "asn": 7303, "clientAcceptEncoding": "gzip, deflate, br", "verifiedBotCategory": "", "country": "AR", "isEUCountry": false, "region": "Buenos Aires F.D.", "tlsClientCiphersSha1": "JZtiTn8H/ntxORk+XXvU2EvNoz8=", "tlsClientAuth": {"certIssuerDNLegacy": "", "certIssuerSKI": "", "certSubjectDNRFC2253": "", "certSubjectDNLegacy": "", "certFingerprintSHA256": "", "certNotBefore": "", "certSKI": "", "certSerial": "", "certIssuerDN": "", "certVerified": "NONE", "certNotAfter": "", "certSubjectDN": "", "certPresented": "0", "certRevoked": "0", "certIssuerSerial": "", "certIssuerDNRFC2253": "", "certFingerprintSHA1": ""}, "tlsClientRandom": "VgVHQx3ASQYAxZtbyu5UQ7fXsrCSw4dMF7j6CJl4uio=", "tlsExportedAuthenticator": {"clientFinished": "0383e177b171496109aa588ba5de30597cf6389556d67ae5eccf5919688bf7d938b378f5d906d6508013d730c841ba34", "clientHandshake": "38a06f5cbf21cfa74345fc57b4ea832b4492ba62ae87c6d29df0f3cb7755898fd906603e013249b286217dc8e46fc6a2", "serverHandshake": "b34816698ba52b548da04febb144f1f648b7a5003a6c990504cdc0a2dc004a318f68b62cf889b03cc0686aeb931112d3", "serverFinished": "0d22d52ca953ad0452c7a8a80e419c13c0d6147e4476dacedc8d6f8052edf03394e9f5748fe7edff199539f6aeafc755"}, "tlsClientHelloLength": "386", "colo": "EZE", "timezone": "America/Argentina/Buenos_Aires", "longitude": "-58.37723", "latitude": "-34.61315", "edgeRequestKeepAliveStatus": 1, "requestPriority": "", "postalCode": "1871", "city": "Buenos Aires", "tlsVersion": "TLSv1.3", "regionCode": "C", "asOrganization": "Telecom Argentina S.A.", "tlsClientExtensionsSha1Le": "6e+q3vPm88rSgMTN/h7WTTxQ2wQ=", "tlsClientExtensionsSha1": "Y7DIC8A6G0/aXviZ8ie/xDbJb7g=", "botManagement": {"corporateProxy": false, "verifiedBot": false, "jsDetection": {"passed": false}, "staticResource": false, "detectionIds": {}, "score": 99}}