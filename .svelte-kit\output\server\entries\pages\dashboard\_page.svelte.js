import { y as head, z as bind_props } from "../../../chunks/index.js";
import { e as escape_html } from "../../../chunks/escaping.js";
function _page($$payload, $$props) {
  let data = $$props["data"];
  const { user } = data;
  head($$payload, ($$payload2) => {
    $$payload2.title = `<title>Dashboard - Azure AD SSO</title>`;
  });
  $$payload.out += `<div class="min-h-screen bg-gray-50"><nav class="bg-white shadow"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="flex justify-between h-16"><div class="flex items-center"><h1 class="text-xl font-semibold text-gray-900">Dashboard</h1></div> <div class="flex items-center space-x-4"><span class="text-sm text-gray-700">Welcome, ${escape_html(user.name)}</span> <button type="button" class="bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded-md text-sm font-medium">Logout</button></div></div></div></nav> <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8"><div class="px-4 py-6 sm:px-0"><div class="border-4 border-dashed border-gray-200 rounded-lg p-8"><div class="text-center"><h2 class="text-2xl font-bold text-gray-900 mb-4">Welcome to your Dashboard</h2> <p class="text-gray-600 mb-8">You have successfully authenticated with Azure Active Directory.</p> <div class="bg-white overflow-hidden shadow rounded-lg max-w-md mx-auto"><div class="px-4 py-5 sm:p-6"><h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">User Information</h3> <dl class="grid grid-cols-1 gap-x-4 gap-y-4 sm:grid-cols-2"><div class="sm:col-span-2"><dt class="text-sm font-medium text-gray-500">Name</dt> <dd class="mt-1 text-sm text-gray-900">${escape_html(user.name)}</dd></div> <div class="sm:col-span-2"><dt class="text-sm font-medium text-gray-500">Email</dt> <dd class="mt-1 text-sm text-gray-900">${escape_html(user.email)}</dd></div> <div class="sm:col-span-2"><dt class="text-sm font-medium text-gray-500">Username</dt> <dd class="mt-1 text-sm text-gray-900">${escape_html(user.preferred_username)}</dd></div> <div class="sm:col-span-2"><dt class="text-sm font-medium text-gray-500">User ID</dt> <dd class="mt-1 text-sm text-gray-900 font-mono text-xs">${escape_html(user.oid)}</dd></div> <div class="sm:col-span-2"><dt class="text-sm font-medium text-gray-500">Tenant ID</dt> <dd class="mt-1 text-sm text-gray-900 font-mono text-xs">${escape_html(user.tid)}</dd></div></dl></div></div> <div class="mt-8 grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3"><div class="bg-white overflow-hidden shadow rounded-lg"><div class="p-5"><div class="flex items-center"><div class="flex-shrink-0"><svg class="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg></div> <div class="ml-5 w-0 flex-1"><dl><dt class="text-sm font-medium text-gray-500 truncate">Authentication Status</dt> <dd class="text-lg font-medium text-gray-900">Authenticated</dd></dl></div></div></div></div> <div class="bg-white overflow-hidden shadow rounded-lg"><div class="p-5"><div class="flex items-center"><div class="flex-shrink-0"><svg class="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path></svg></div> <div class="ml-5 w-0 flex-1"><dl><dt class="text-sm font-medium text-gray-500 truncate">Security</dt> <dd class="text-lg font-medium text-gray-900">JWT Validated</dd></dl></div></div></div></div> <div class="bg-white overflow-hidden shadow rounded-lg"><div class="p-5"><div class="flex items-center"><div class="flex-shrink-0"><svg class="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path></svg></div> <div class="ml-5 w-0 flex-1"><dl><dt class="text-sm font-medium text-gray-500 truncate">Platform</dt> <dd class="text-lg font-medium text-gray-900">Cloudflare Workers</dd></dl></div></div></div></div></div></div></div></div></main></div>`;
  bind_props($$props, { data });
}
export {
  _page as default
};
