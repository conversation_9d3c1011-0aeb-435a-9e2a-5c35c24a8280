import { json } from "@sveltejs/kit";
import { A as AuthService } from "../../../chunks/auth.js";
const GET = async ({ request, platform }) => {
  try {
    const cookieHeader = request.headers.get("cookie");
    const env = platform?.env && Object.keys(platform.env).length > 0 ? platform.env : {
      AZURE_CLIENT_ID: process.env.AZURE_CLIENT_ID || "",
      AZURE_CLIENT_SECRET: process.env.AZURE_CLIENT_SECRET || "",
      AZURE_TENANT_ID: process.env.AZURE_TENANT_ID || "",
      REDIRECT_URI: process.env.REDIRECT_URI || "",
      SESSION_SECRET: process.env.SESSION_SECRET || "",
      COOKIE_DOMAIN: process.env.COOKIE_DOMAIN || "localhost"
    };
    const authService = new AuthService(env);
    const session = authService.parseSession(cookieHeader);
    return json({
      hasCookieHeader: !!cookieHeader,
      cookieHeader,
      hasSession: !!session,
      sessionUser: session?.user?.name || null,
      environment: platform?.env || {},
      timestamp: (/* @__PURE__ */ new Date()).toISOString()
    });
  } catch (error) {
    return json({
      error: error instanceof Error ? error.message : "Unknown error",
      timestamp: (/* @__PURE__ */ new Date()).toISOString()
    }, { status: 500 });
  }
};
export {
  GET
};
