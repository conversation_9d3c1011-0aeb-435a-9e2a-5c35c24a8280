import{C as o,w as v,D as f,F as b,q as n,G as d,I as _,J as y,u as c,K as x,L as h,M as C,N as S,O as k,P as w}from"./-lpZNR2G.js";import{a as A,m as D,u as M}from"./oU013O0A.js";import{c as O}from"./DHFUQ1My.js";function P(){var t;return f===null&&b(),((t=f).ac??(t.ac=new AbortController)).signal}function p(t){n===null&&o(),y&&n.l!==null?r(n).m.push(t):v(()=>{const e=c(t);if(typeof e=="function")return e})}function j(t){n===null&&o(),p(()=>()=>c(t))}function E(t,e,{bubbles:s=!1,cancelable:l=!1}={}){return new CustomEvent(t,{detail:e,bubbles:s,cancelable:l})}function U(){const t=n;return t===null&&o(),(e,s,l)=>{var u;const a=(u=t.s.$$events)==null?void 0:u[e];if(a){const m=d(a)?a.slice():[a],i=E(e,s,l);for(const g of m)g.call(t.x,i);return!i.defaultPrevented}return!0}}function $(t){n===null&&o(),n.l===null&&_(),r(n).b.push(t)}function q(t){n===null&&o(),n.l===null&&_(),r(n).a.push(t)}function r(t){var e=t.l;return e.u??(e.u={a:[],b:[],m:[]})}const I=Object.freeze(Object.defineProperty({__proto__:null,afterUpdate:q,beforeUpdate:$,createEventDispatcher:U,createRawSnippet:O,flushSync:x,getAbortSignal:P,getAllContexts:h,getContext:C,hasContext:S,hydrate:A,mount:D,onDestroy:j,onMount:p,setContext:k,tick:w,unmount:M,untrack:c},Symbol.toStringTag,{value:"Module"}));export{p as o,I as s};
