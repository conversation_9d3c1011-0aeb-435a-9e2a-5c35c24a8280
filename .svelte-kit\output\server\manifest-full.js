export const manifest = (() => {
function __memo(fn) {
	let value;
	return () => value ??= (value = fn());
}

return {
	appDir: "_app",
	appPath: "_app",
	assets: new Set(["favicon.svg"]),
	mimeTypes: {".svg":"image/svg+xml"},
	_: {
		client: {start:"_app/immutable/entry/start.CWO0m9OG.js",app:"_app/immutable/entry/app.DdW0kJGs.js",imports:["_app/immutable/entry/start.CWO0m9OG.js","_app/immutable/chunks/BRLLTahE.js","_app/immutable/chunks/Dcy8ifye.js","_app/immutable/chunks/DfYgktsV.js","_app/immutable/chunks/DZXC8P3u.js","_app/immutable/chunks/imUBLVoF.js","_app/immutable/entry/app.DdW0kJGs.js","_app/immutable/chunks/DfYgktsV.js","_app/immutable/chunks/DZXC8P3u.js","_app/immutable/chunks/CWj6FrbW.js","_app/immutable/chunks/Dcy8ifye.js","_app/immutable/chunks/imUBLVoF.js","_app/immutable/chunks/_pqzL3p7.js","_app/immutable/chunks/5GeUa9TP.js","_app/immutable/chunks/BrXvW6T5.js"],stylesheets:[],fonts:[],uses_env_dynamic_public:false},
		nodes: [
			__memo(() => import('./nodes/0.js')),
			__memo(() => import('./nodes/1.js')),
			__memo(() => import('./nodes/2.js')),
			__memo(() => import('./nodes/3.js')),
			__memo(() => import('./nodes/4.js')),
			__memo(() => import('./nodes/5.js'))
		],
		routes: [
			{
				id: "/",
				pattern: /^\/$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 2 },
				endpoint: null
			},
			{
				id: "/api/auth/login",
				pattern: /^\/api\/auth\/login\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/auth/login/_server.ts.js'))
			},
			{
				id: "/callback",
				pattern: /^\/callback\/?$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 3 },
				endpoint: null
			},
			{
				id: "/dashboard",
				pattern: /^\/dashboard\/?$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 4 },
				endpoint: null
			},
			{
				id: "/login",
				pattern: /^\/login\/?$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 5 },
				endpoint: null
			},
			{
				id: "/logout",
				pattern: /^\/logout\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/logout/_server.ts.js'))
			}
		],
		prerendered_routes: new Set([]),
		matchers: async () => {
			
			return {  };
		},
		server_assets: {}
	}
}
})();
