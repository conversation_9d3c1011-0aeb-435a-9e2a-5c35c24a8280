import { createRemoteJWKSet, jwtVerify } from "jose";
import { serialize, parse } from "cookie";
import { redirect } from "@sveltejs/kit";
let jwksCache = null;
class AuthService {
  clientId;
  clientSecret;
  tenantId;
  redirectUri;
  sessionSecret;
  cookieDomain;
  constructor(env) {
    this.clientId = env.AZURE_CLIENT_ID;
    this.clientSecret = env.AZURE_CLIENT_SECRET;
    this.tenantId = env.AZURE_TENANT_ID;
    this.redirectUri = env.REDIRECT_URI;
    this.sessionSecret = env.SESSION_SECRET;
    this.cookieDomain = env.COOKIE_DOMAIN;
    if (!this.clientId || !this.clientSecret || !this.tenantId || !this.redirectUri || !this.sessionSecret) {
      throw new Error("Missing required Azure AD configuration");
    }
  }
  /**
   * Get Azure AD OAuth2 authorization URL
   */
  getAuthUrl(state) {
    const params = new URLSearchParams({
      client_id: this.clientId,
      response_type: "code",
      redirect_uri: this.redirectUri,
      scope: "openid profile email",
      response_mode: "query",
      ...state && { state }
    });
    return `https://login.microsoftonline.com/${this.tenantId}/oauth2/v2.0/authorize?${params}`;
  }
  /**
   * Exchange authorization code for access token
   */
  async exchangeCodeForToken(code) {
    const tokenUrl = `https://login.microsoftonline.com/${this.tenantId}/oauth2/v2.0/token`;
    const body = new URLSearchParams({
      client_id: this.clientId,
      client_secret: this.clientSecret,
      code,
      grant_type: "authorization_code",
      redirect_uri: this.redirectUri,
      scope: "openid profile email"
    });
    const response = await fetch(tokenUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded"
      },
      body: body.toString()
    });
    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Token exchange failed: ${error}`);
    }
    return await response.json();
  }
  /**
   * Get JWKS for token validation
   */
  getJWKS() {
    if (!jwksCache) {
      const jwksUri = `https://login.microsoftonline.com/${this.tenantId}/discovery/v2.0/keys`;
      jwksCache = createRemoteJWKSet(new URL(jwksUri));
    }
    return jwksCache;
  }
  /**
   * Validate and decode JWT token
   */
  async validateToken(token) {
    try {
      const jwks = this.getJWKS();
      const { payload } = await jwtVerify(token, jwks, {
        issuer: `https://login.microsoftonline.com/${this.tenantId}/v2.0`,
        audience: this.clientId
      });
      return {
        oid: payload.oid,
        name: payload.name,
        email: payload.email,
        preferred_username: payload.preferred_username,
        tid: payload.tid
      };
    } catch (error) {
      throw new Error(`Token validation failed: ${error}`);
    }
  }
  /**
   * Create session cookie
   */
  createSessionCookie(sessionData) {
    const sessionJson = JSON.stringify(sessionData);
    const sessionBase64 = Buffer.from(sessionJson).toString("base64");
    return serialize("session", sessionBase64, {
      httpOnly: true,
      secure: true,
      sameSite: "strict",
      maxAge: 60 * 60 * 24,
      // 24 hours
      path: "/",
      domain: this.cookieDomain === "localhost" ? void 0 : this.cookieDomain
    });
  }
  /**
   * Parse session from cookie
   */
  parseSession(cookieHeader) {
    if (!cookieHeader) return null;
    try {
      const cookies = parse(cookieHeader);
      const sessionCookie = cookies.session;
      if (!sessionCookie) return null;
      const sessionJson = Buffer.from(sessionCookie, "base64").toString("utf-8");
      const sessionData = JSON.parse(sessionJson);
      if (Date.now() > sessionData.expiresAt) {
        return null;
      }
      return sessionData;
    } catch {
      return null;
    }
  }
  /**
   * Create logout cookie (clears session)
   */
  createLogoutCookie() {
    return serialize("session", "", {
      httpOnly: true,
      secure: true,
      sameSite: "strict",
      maxAge: 0,
      path: "/",
      domain: this.cookieDomain === "localhost" ? void 0 : this.cookieDomain
    });
  }
  /**
   * Get Azure AD logout URL
   */
  getLogoutUrl() {
    const params = new URLSearchParams({
      post_logout_redirect_uri: this.redirectUri.replace("/callback", "/login")
    });
    return `https://login.microsoftonline.com/${this.tenantId}/oauth2/v2.0/logout?${params}`;
  }
}
async function getAuthenticatedUser(event) {
  const authService = new AuthService(event.platform?.env || {});
  const session = authService.parseSession(event.request.headers.get("cookie"));
  if (!session) return null;
  try {
    await authService.validateToken(session.accessToken);
    return session.user;
  } catch {
    return null;
  }
}
async function requireAuth(event) {
  const user = await getAuthenticatedUser(event);
  if (!user) {
    throw redirect(302, "/login");
  }
  return user;
}
export {
  AuthService as A,
  getAuthenticatedUser as g,
  requireAuth as r
};
