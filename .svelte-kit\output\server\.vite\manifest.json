{".svelte-kit/generated/server/internal.js": {"file": "internal.js", "name": "internal", "src": ".svelte-kit/generated/server/internal.js", "isEntry": true, "imports": ["_internal.js"]}, "_auth.js": {"file": "chunks/auth.js", "name": "auth"}, "_client.js": {"file": "chunks/client.js", "name": "client", "imports": ["_exports.js", "_equality.js"]}, "_equality.js": {"file": "chunks/equality.js", "name": "equality"}, "_escaping.js": {"file": "chunks/escaping.js", "name": "escaping"}, "_exports.js": {"file": "chunks/exports.js", "name": "exports", "imports": ["_equality.js"]}, "_index.js": {"file": "chunks/index.js", "name": "index"}, "_internal.js": {"file": "chunks/internal.js", "name": "internal", "imports": ["_index.js", "_equality.js"], "dynamicImports": ["src/hooks.server.ts"]}, "node_modules/@sveltejs/kit/src/runtime/components/svelte-5/error.svelte": {"file": "entries/fallbacks/error.svelte.js", "name": "entries/fallbacks/error.svelte", "src": "node_modules/@sveltejs/kit/src/runtime/components/svelte-5/error.svelte", "isEntry": true, "imports": ["_escaping.js", "_index.js", "_client.js"]}, "node_modules/@sveltejs/kit/src/runtime/server/index.js": {"file": "index.js", "name": "index", "src": "node_modules/@sveltejs/kit/src/runtime/server/index.js", "isEntry": true, "imports": ["_index.js", "_internal.js", "_exports.js"]}, "src/hooks.server.ts": {"file": "chunks/hooks.server.js", "name": "hooks.server", "src": "src/hooks.server.ts", "isDynamicEntry": true, "imports": ["_auth.js"]}, "src/routes/+layout.svelte": {"file": "entries/pages/_layout.svelte.js", "name": "entries/pages/_layout.svelte", "src": "src/routes/+layout.svelte", "isEntry": true, "css": ["_app/immutable/assets/_layout.BVM9TFbn.css"]}, "src/routes/+page.server.ts": {"file": "entries/pages/_page.server.ts.js", "name": "entries/pages/_page.server.ts", "src": "src/routes/+page.server.ts", "isEntry": true, "imports": ["_auth.js"]}, "src/routes/+page.svelte": {"file": "entries/pages/_page.svelte.js", "name": "entries/pages/_page.svelte", "src": "src/routes/+page.svelte", "isEntry": true, "imports": ["_index.js", "_escaping.js"]}, "src/routes/api/auth/login/+server.ts": {"file": "entries/endpoints/api/auth/login/_server.ts.js", "name": "entries/endpoints/api/auth/login/_server.ts", "src": "src/routes/api/auth/login/+server.ts", "isEntry": true, "imports": ["_auth.js"]}, "src/routes/callback/+page.server.ts": {"file": "entries/pages/callback/_page.server.ts.js", "name": "entries/pages/callback/_page.server.ts", "src": "src/routes/callback/+page.server.ts", "isEntry": true, "imports": ["_auth.js"]}, "src/routes/dashboard/+page.server.ts": {"file": "entries/pages/dashboard/_page.server.ts.js", "name": "entries/pages/dashboard/_page.server.ts", "src": "src/routes/dashboard/+page.server.ts", "isEntry": true, "imports": ["_auth.js"]}, "src/routes/dashboard/+page.svelte": {"file": "entries/pages/dashboard/_page.svelte.js", "name": "entries/pages/dashboard/_page.svelte", "src": "src/routes/dashboard/+page.svelte", "isEntry": true, "imports": ["_index.js", "_escaping.js"]}, "src/routes/login/+page.svelte": {"file": "entries/pages/login/_page.svelte.js", "name": "entries/pages/login/_page.svelte", "src": "src/routes/login/+page.svelte", "isEntry": true, "imports": ["_index.js", "_client.js", "_escaping.js"], "css": ["_app/immutable/assets/_page.tn0RQdqM.css"]}, "src/routes/logout/+server.ts": {"file": "entries/endpoints/logout/_server.ts.js", "name": "entries/endpoints/logout/_server.ts", "src": "src/routes/logout/+server.ts", "isEntry": true, "imports": ["_auth.js"]}}