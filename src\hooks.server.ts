import type { Handle } from '@sveltejs/kit';
import { redirect } from '@sveltejs/kit';
import { getAuthenticatedUser } from '$lib/auth';

// Routes that require authentication
const protectedRoutes = ['/', '/dashboard'];

// Routes that should redirect authenticated users (like login page)
const publicOnlyRoutes = ['/login'];

export const handle: Handle = async ({ event, resolve }) => {
	// Add security headers
	const response = await resolve(event, {
		transformPageChunk: ({ html }) => html
	});

	// Set security headers
	response.headers.set('X-Frame-Options', 'DENY');
	response.headers.set('X-Content-Type-Options', 'nosniff');
	response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
	response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');
	
	// Content Security Policy
	const csp = [
		"default-src 'self'",
		"script-src 'self' 'unsafe-inline'",
		"style-src 'self' 'unsafe-inline'",
		"img-src 'self' data: https:",
		"font-src 'self'",
		"connect-src 'self' https://login.microsoftonline.com",
		"frame-ancestors 'none'",
		"base-uri 'self'",
		"form-action 'self' https://login.microsoftonline.com"
	].join('; ');
	
	response.headers.set('Content-Security-Policy', csp);

	// HSTS header for HTTPS
	if (event.url.protocol === 'https:') {
		response.headers.set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
	}

	// Check authentication for protected routes
	const pathname = event.url.pathname;
	
	// Skip auth check for public routes and API endpoints
	if (pathname.startsWith('/callback') || 
		pathname.startsWith('/logout') || 
		pathname.startsWith('/api/') ||
		pathname.startsWith('/_app/') ||
		pathname.includes('.')) {
		return response;
	}

	try {
		const user = await getAuthenticatedUser(event);

		// Redirect authenticated users away from public-only routes
		if (user && publicOnlyRoutes.includes(pathname)) {
			throw redirect(302, '/dashboard');
		}

		// Redirect unauthenticated users from protected routes
		if (!user && protectedRoutes.includes(pathname)) {
			throw redirect(302, '/login');
		}

		// Add user to locals for use in pages
		event.locals.user = user || undefined;

	} catch (error) {
		// If it's a redirect, let it through
		if (error instanceof Response && error.status >= 300 && error.status < 400) {
			throw error;
		}
		
		// For other auth errors, redirect to login
		if (protectedRoutes.includes(pathname)) {
			throw redirect(302, '/login');
		}
	}

	return response;
};
