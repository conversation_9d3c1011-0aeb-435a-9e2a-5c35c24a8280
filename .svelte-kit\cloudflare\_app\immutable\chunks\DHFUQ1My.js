import{b as _,h as i,d,Q as h,R as l,S as m,E as u,m as v,T as y,V as b,o as c,W as g}from"./-lpZNR2G.js";function R(t,s,...n){var a=t,e=y,r;_(()=>{e!==(e=s())&&(r&&(b(r),r=null),r=v(()=>e(a,...n)))},u),i&&(a=c)}function T(t){return(s,...n)=>{var f;var a=t(...n),e;if(i)e=c,d();else{var r=a.render().trim(),p=h(r);e=g(p),s.before(e)}const o=(f=a.setup)==null?void 0:f.call(a,e);l(e,e),typeof o=="function"&&m(o)}}export{T as c,R as s};
